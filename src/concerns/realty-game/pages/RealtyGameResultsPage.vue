<template>
  <div class="realty-game-results-page">
    <div class="max-ctr q-pa-lg">
      <!-- Loading State -->
      <div v-if="isLoading"
           class="loading-container text-center q-pa-xl">
        <q-spinner color="primary"
                   size="3em" />
        <div class="q-mt-md text-h6">Loading Results...</div>
        <div class="text-body2 text-grey-7">Crunching the numbers and comparing scores</div>
      </div>

      <!-- Error State -->
      <div v-else-if="error"
           class="error-container text-center q-pa-xl">
        <q-icon name="error"
                color="negative"
                size="3em" />
        <div class="q-mt-md text-h6 text-negative">Failed to Load Results</div>
        <div class="text-body2 text-grey-7 q-mb-lg">{{ error }}</div>
        <q-btn color="primary"
               label="Try Again"
               @click="loadResults" />
      </div>

      <!-- Results Content -->
      <div v-else-if="results"
           class="results-content">
        <!-- Results Header -->
        <div class="results-header q-mb-xl text-center">
          <div class="performance-badge q-mb-lg">
            <q-icon :name="playerResults.performance_rating?.icon"
                    :color="playerResults.performance_rating?.color"
                    size="4em" />
            <div class="text-h4 text-weight-bold q-mt-md"
                 :class="`text-${playerResults.performance_rating?.color}`">
              {{ playerResults.performance_rating?.rating }}
            </div>
            <div class="text-h6 text-grey-7">{{ playerResults.total_score }} / {{ playerResults.max_possible_score }}
              points</div>
          </div>
          <h1 class="text-h4 text-weight-bold text-primary q-mb-sm">Challenge Complete!</h1>
          <p class="text-body1 text-grey-7">Here's how you performed on each property</p>
        </div>

        <!-- Results Table -->
        <q-card class="results-card q-mb-lg"
                flat
                bordered>
          <q-card-section class="q-pa-lg">
            <!-- Note: :rows is now bound to gameBreakdown -->
            <q-table :rows="gameBreakdown"
                     :columns="resultsColumns"
                     row-key="uuid"
                     flat
                     :pagination="{ rowsPerPage: 0 }"
                     class="results-table">
              <!-- The v-slot templates remain largely the same because the `game_results` array structure is the same -->
              <template v-slot:body-cell-property="props">
                <q-td :props="props">
                  <div class="property-cell"
                       style="overflow: auto;">
                    <div class="text-weight-medium">{{ props.row.estimate_title }}</div>
                    <div class="text-caption text-grey-6">{{ props.row.estimate_vicinity }}</div>
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-guess="props">
                <q-td :props="props">
                  <div class="text-weight-medium">
                    {{ formatPriceWithBothCurrencies(props.row.guessed_price_amount_cents, props.row.estimate_currency,
                    false) }}
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-actual="props">
                <q-td :props="props">
                  <div class="text-weight-medium">
                    {{ formatPriceWithBothCurrencies(props.row.price_at_time_of_estimate_cents,
                      props.row.estimate_currency, false) }}
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-difference="props">
                <q-td :props="props">
                  <q-chip :color="Math.abs(props.row.percentage_above_or_below) <= 10 ? 'positive' : Math.abs(props.row.percentage_above_or_below) <= 25 ? 'warning' : 'negative'"
                          text-color="white"
                          size="sm">
                    {{ props.row.percentage_above_or_below > 0 ? '+' : '' }}{{
                      props.row.percentage_above_or_below?.toFixed(1) }}%
                  </q-chip>
                </q-td>
              </template>

              <template v-slot:body-cell-score="props">
                <q-td :props="props">
                  <div class="score-cell">
                    <q-circular-progress :value="props.row.score_for_guess || 0"
                                         size="40px"
                                         :thickness="0.15"
                                         :color="getScoreColor(props.row.score_for_guess || 0)"
                                         track-color="grey-3"
                                         class="q-mr-sm">
                      <div class="text-caption text-weight-bold">{{ props.row.score_for_guess || 0 }}</div>
                    </q-circular-progress>
                  </div>
                </q-td>
              </template>
            </q-table>
          </q-card-section>
        </q-card>

        <!-- Comparison Summary -->
        <q-card v-if="comparisonSummary.length > 0"
                class="comparison-card q-mb-lg"
                flat
                bordered>
          <q-card-section class="q-pa-lg">
            <div class="text-h6 q-mb-md">
              <q-icon name="people"
                      color="primary"
                      size="sm"
                      class="q-mr-sm" />
              How You Compare to Other Players
            </div>
            <!-- The isLoadingComparisons spinner is no longer needed as it all loads at once -->
            <div>
              <div v-for="(propertyData, index) in comparisonSummary"
                   :key="index"
                   class="property-comparison q-mb-lg">
                <div class="comparison-header q-mb-md">
                  <div v-if="propertyData.property_url"
                       class="text-subtitle1 text-weight-medium">
                    <a :href="propertyData.property_url">{{ propertyData.property_title }}</a>
                  </div>
                  <div v-else
                       class="text-subtitle1 text-weight-medium">{{ propertyData.property_title }}</div>
                  <div class="text-caption text-grey-6">{{ propertyData.property_vicinity }}</div>
                </div>

                <!-- Your guess vs others -->
                <div class="comparison-stats q-mb-md">
                  <div class="row q-col-gutter-md">
                    <div class="col-12 col-md-4">
                      <q-card flat
                              bordered
                              class="stat-card">
                        <q-card-section class="text-center q-pa-md">
                          <div class="text-h6 text-primary">{{ propertyData.your_guess_formatted }}</div>
                          <div class="text-caption text-grey-6">Your Guess</div>
                        </q-card-section>
                      </q-card>
                    </div>
                    <div class="col-12 col-md-4">
                      <q-card flat
                              bordered
                              class="stat-card">
                        <q-card-section class="text-center q-pa-md">
                          <div class="text-h6 text-secondary">{{ propertyData.average_guess_formatted }}</div>
                          <div class="text-caption text-grey-6">Average Guess</div>
                        </q-card-section>
                      </q-card>
                    </div>
                    <div class="col-12 col-md-4">
                      <q-card flat
                              bordered
                              class="stat-card">
                        <q-card-section class="text-center q-pa-md">
                          <div class="text-h6 text-positive">{{ propertyData.actual_price_formatted }}</div>
                          <div class="text-caption text-grey-6">Actual Price</div>
                        </q-card-section>
                      </q-card>
                    </div>
                  </div>
                </div>

                <!-- Performance ranking -->
                <div class="performance-ranking">
                  <div class="text-subtitle2 q-mb-sm">Your Performance</div>
                  <div class="ranking-info">
                    <q-chip :color="propertyData.ranking.color"
                            text-color="white"
                            icon="emoji_events">
                      Ranked {{ propertyData.ranking.rank }} of {{ propertyData.ranking.total_players }}
                    </q-chip>
                    <span class="q-ml-md text-body2 text-grey-7">{{ propertyData.ranking.performance_text }}</span>
                  </div>
                </div>
                <q-separator v-if="index < comparisonSummary.length - 1"
                             class="q-mt-lg" />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- No Results -->
      <div v-else
           class="no-results-container text-center q-pa-xl">
        <q-icon name="search_off"
                color="grey-5"
                size="3em" />
        <div class="q-mt-md text-h6 text-grey-7">No Results Found</div>
        <div class="text-body2 text-grey-6 q-mb-lg">
          We couldn't find any results for this game session.
        </div>
        <q-btn color="primary"
               label="Start New Game"
               @click="startNewGame" />
      </div>
      <q-no-ssr>
        <div v-if="gameCommunitiesDetailsCalc.show"
             class="results-actions q-pa-md text-center bg-grey-2 rounded-borders shadow-2">
          <!-- <div class="q-mb-md">
            <q-icon name="fab fa-reddit-alien"
                    size="sm"
                    color="red-6"
                    class="q-mr-sm" />
            <span class="text-subtitle1 text-grey-8">Join the discussion on Reddit:</span>
            <a :href="gameCommunitiesDetailsCalc.redditCommunity.url"
               target="_blank"
               class="text-blue-7 hover:text-blue-9 q-ml-sm text-weight-medium">
              {{ gameCommunitiesDetailsCalc.redditCommunity.name || gameCommunitiesDetailsCalc.redditCommunity.url }}
            </a>
          </div>

          <div class="q-mt-lg">
            <h6 class="text-h6 text-grey-9 q-mb-sm">Other price guess games you might like:</h6>
            <div class="row justify-center q-gutter-sm">
              <div v-for="(game, index) in gameCommunitiesDetailsCalc.relevantGames"
                   :key="index"
                   class="col-auto">
                <q-chip :to="game"
                        target="_blank"
                        clickable
                        color="blue-grey-1"
                        text-color="blue-8"
                        class="hover:bg-blue-3">
                  <a :href="`https://${game}`">
                    {{ `https://${game}` }}
                  </a>
                </q-chip>
              </div>
            </div>
          </div> -->

          <!-- Action Buttons -->
          <!-- <div class="q-mt-lg row justify-center q-gutter-md">
            <q-btn color="primary"
                   label="Play Again"
                   icon="refresh"
                   size="lg"
                   rounded
                   unelevated
                   @click="playAgain" />
            <q-btn color="secondary"
                   label="Share Results"
                   icon="share"
                   size="lg"
                   rounded
                   outline
                   @click="shareResults" />
          </div> -->

          <q-card-section class="text-center">
            <div class="text-h6 q-mb-sm">
              🎮 Join the Community Discussion
            </div>
            <q-btn :label="gameCommunitiesDetailsCalc.redditCommunity.url"
                   :href="gameCommunitiesDetailsCalc.redditCommunity.url"
                   type="a"
                   color="red"
                   target="_blank"
                   icon="mdi-reddit"
                   flat />
          </q-card-section>

          <!-- <q-separator spaced /> -->

          <q-card-section>
            <div class="text-subtitle1 q-mb-sm text-center">
              🧠 Other Price Guess Games You Might Like
            </div>
            <q-list bordered
                    separator
                    class="rounded-borders">
              <q-item v-for="(relevantGame, index) in gameCommunitiesDetailsCalc.relevantGames"
                      :key="index"
                      clickable>
                <q-item-section>
                  <a :href="`https://${relevantGame}`"
                     target="_blank"
                     class="text-primary">
                    {{ `https://${relevantGame}` }}
                  </a>
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
        </div>
      </q-no-ssr>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
// Import the new composable
import { useServerRealtyGameResults } from '../composables/useServerRealtyGameResults'
import { useCurrencyConverter } from '../composables/useCurrencyConverter'
import { useRealtyGameStorage } from '../composables/useRealtyGameStorage'

const props = defineProps({
  gameSessionId: {
    type: String,
    required: true
  },
  gameCommunitiesDetails: {
    type: Object
  }
})

const $router = useRouter()
const $q = useQuasar()

// Initialize the new composable
const {
  isLoading,
  error,
  results,
  playerResults,
  comparisonSummary,
  gameBreakdown,
  fetchResults,
  formatPrice,
  getScoreColor
} = useServerRealtyGameResults()

// Initialize currency converter
const {
  setCurrency,
  formatPriceWithBothCurrencies
} = useCurrencyConverter()

// Initialize storage to get currency selection
const {
  getCurrencySelection
} = useRealtyGameStorage()

// Set currency from session
const sessionCurrency = getCurrencySelection(props.gameSessionId)
if (sessionCurrency) {
  setCurrency(sessionCurrency)
}

// Computed properties for the component
const gameCommunitiesDetailsCalc = computed(() => {
  // This logic is client-specific and remains here
  if (typeof window !== 'undefined') {
    // const currentHost = location.host;
    // const hosts = ['nuneaton.propertysquares.com', 'brum.propertysquares.com', 'brum-houses.propertysquares.com'];
    // const relevantGames = hosts.filter(host => host !== currentHost);
    // let redC = {
    //   url: 'https://www.reddit.com/r/propertysquares/',
    //   text: 'Join the discussion on reddit!'
    // };
    // if (currentHost === 'nuneaton.propertysquares.com') {
    //   redC.url = "https://www.reddit.com/r/nuneaton/";
    // }
    let showComDetails = false
    if (props.gameCommunitiesDetails.redditCommunity) {
      showComDetails = true
    }
    return {
      show: showComDetails,
      redditCommunity: props.gameCommunitiesDetails.redditCommunity,
      relevantGames: props.gameCommunitiesDetails.relevantGames
    };
  } else {
    return { show: false };
  }
});

const resultsColumns = computed(() => [
  { name: 'property', label: 'Property', field: 'property', align: 'left', style: 'width: 30%' },
  { name: 'guess', label: 'Your Guess', field: 'guess', align: 'right', style: 'width: 20%' },
  { name: 'actual', label: 'Actual Price', field: 'actual', align: 'right', style: 'width: 20%' },
  { name: 'difference', label: 'Difference', field: 'difference', align: 'center', style: 'width: 15%' },
  { name: 'score', label: 'Score', field: 'score', align: 'center', style: 'width: 15%' }
]);

// Methods
const loadResults = async () => {
  await fetchResults(props.gameSessionId);
}

const playAgain = () => {
  $router.push({ name: 'rPriceGuessStart' });
}

const startNewGame = () => {
  $router.push({ name: 'rPriceGuessStart' });
}

// Share logic remains the same, but it can now use the server data
const shareResults = () => {
  const url = window.location.href;
  const text = `I just completed the Property Price Challenge! Score: ${playerResults.value.total_score}/${playerResults.value.max_possible_score} (${playerResults.value.performance_rating.rating})`;

  if (navigator.share) {
    navigator.share({
      title: 'Property Price Challenge Results',
      text: text,
      url: url
    }).catch(() => {
      fallbackShare(url, text);
    });
  } else {
    fallbackShare(url, text);
  }
}

const fallbackShare = (url, text) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(url).then(() => {
      $q.notify({
        message: 'Results URL copied to clipboard!',
        icon: 'content_copy',
        color: 'positive'
      });
    });
  }
}

// Initialize on mount
onMounted(() => {
  loadResults();
})
</script>

<style scoped>
.realty-game-results-page {
  /* background-color: #fafafa; */
  min-height: 100vh;
}

.max-ctr {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container,
.error-container,
.no-results-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.results-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.performance-badge {
  display: inline-block;
  padding: 2rem;
  /* background: #f8f9fa; */
  border-radius: 16px;
  border: 2px solid #e9ecef;
}

.session-info {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.results-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.results-table {
  border-radius: 8px;
}

.property-cell {
  max-width: 200px;
}

.score-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.comparison-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.property-comparison {
  border-left: 4px solid #e0e0e0;
  padding-left: 1rem;
}

.comparison-header {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 0.5rem;
}

.stat-card {
  border-radius: 8px;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.performance-ranking {
  /* background: #f0f4f8; */
  border-radius: 8px;
  padding: 1rem;
}

.ranking-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.results-actions {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Button improvements */
.q-btn {
  text-transform: none;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .max-ctr {
    padding: 1rem;
  }

  .results-header {
    padding: 1rem;
  }

  .session-info {
    flex-direction: column;
    align-items: center;
  }

  .comparison-stats .row {
    flex-direction: column;
  }

  .ranking-info {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>