import { ref, computed } from 'vue'
import currencyRatesData from '../data/currencyRates.json'

export function useCurrencyConverter() {
  // Reactive state
  const selectedCurrency = ref('GBP') // Default to GBP
  const currencyRates = ref(currencyRatesData.rates)
  const baseCurrency = ref(currencyRatesData.baseCurrency)
  const isLoading = ref(false)
  const error = ref(null)

  // Computed properties
  const availableCurrencies = computed(() => {
    return Object.entries(currencyRates.value).map(([code, data]) => ({
      code,
      name: data.name,
      symbol: data.symbol,
      rate: data.rate
    }))
  })

  const selectedCurrencyData = computed(() => {
    return currencyRates.value[selectedCurrency.value] || currencyRates.value.GBP
  })

  // Methods
  const convertPrice = (priceInCents, fromCurrency, toCurrency = null) => {
    if (!priceInCents || priceInCents === 0) return 0
    
    const targetCurrency = toCurrency || selectedCurrency.value
    const fromRate = currencyRates.value[fromCurrency]?.rate || 1
    const toRate = currencyRates.value[targetCurrency]?.rate || 1
    
    // Convert to base currency first, then to target currency
    const priceInBaseCurrency = priceInCents / fromRate
    const convertedPrice = priceInBaseCurrency * toRate
    
    return Math.round(convertedPrice)
  }

  const formatPrice = (priceInCents, currency = null, options = {}) => {
    const targetCurrency = currency || selectedCurrency.value
    const currencyData = currencyRates.value[targetCurrency]
    
    if (!priceInCents || !currencyData) {
      return 'Price not available'
    }

    const amount = priceInCents / 100
    
    try {
      return new Intl.NumberFormat('en-UK', {
        style: 'currency',
        currency: targetCurrency,
        minimumFractionDigits: options.showDecimals ? 2 : 0,
        maximumFractionDigits: options.showDecimals ? 2 : 0,
      }).format(amount)
    } catch (e) {
      // Fallback formatting if Intl.NumberFormat fails
      return `${currencyData.symbol}${amount.toLocaleString('en-UK', {
        minimumFractionDigits: options.showDecimals ? 2 : 0,
        maximumFractionDigits: options.showDecimals ? 2 : 0,
      })}`
    }
  }

  const formatPriceWithBothCurrencies = (priceInCents, originalCurrency, showOriginalFirst = true) => {
    if (!priceInCents || selectedCurrency.value === originalCurrency) {
      return formatPrice(priceInCents, originalCurrency)
    }

    const convertedPrice = convertPrice(priceInCents, originalCurrency, selectedCurrency.value)
    const originalFormatted = formatPrice(priceInCents, originalCurrency)
    const convertedFormatted = formatPrice(convertedPrice, selectedCurrency.value)

    if (showOriginalFirst) {
      return `${originalFormatted} (${convertedFormatted})`
    } else {
      return `${convertedFormatted} (${originalFormatted})`
    }
  }

  const getCurrencySymbol = (currency = null) => {
    const targetCurrency = currency || selectedCurrency.value
    return currencyRates.value[targetCurrency]?.symbol || '$'
  }

  const setCurrency = (currencyCode) => {
    if (currencyRates.value[currencyCode]) {
      selectedCurrency.value = currencyCode
    }
  }

  // API function to fetch live rates (optional enhancement)
  const fetchLiveRates = async () => {
    isLoading.value = true
    error.value = null
    
    try {
      // This would be implemented if you have a live currency API
      // For now, we'll just use the hardcoded rates
      console.log('Using hardcoded currency rates')
      return currencyRates.value
    } catch (err) {
      error.value = 'Failed to fetch live currency rates'
      console.error('Currency rate fetch error:', err)
      // Fall back to hardcoded rates
      return currencyRates.value
    } finally {
      isLoading.value = false
    }
  }

  // Validation helpers
  const isValidCurrency = (currencyCode) => {
    return !!currencyRates.value[currencyCode]
  }

  const getConversionRate = (fromCurrency, toCurrency) => {
    const fromRate = currencyRates.value[fromCurrency]?.rate || 1
    const toRate = currencyRates.value[toCurrency]?.rate || 1
    return toRate / fromRate
  }

  // Popular currencies for quick selection
  const popularCurrencies = computed(() => {
    const popular = ['GBP', 'USD', 'EUR', 'CAD', 'AUD', 'JPY']
    return popular.map(code => ({
      code,
      name: currencyRates.value[code]?.name,
      symbol: currencyRates.value[code]?.symbol
    })).filter(item => item.name)
  })

  return {
    // State
    selectedCurrency,
    currencyRates,
    baseCurrency,
    isLoading,
    error,

    // Computed
    availableCurrencies,
    selectedCurrencyData,
    popularCurrencies,

    // Methods
    convertPrice,
    formatPrice,
    formatPriceWithBothCurrencies,
    getCurrencySymbol,
    setCurrency,
    fetchLiveRates,
    isValidCurrency,
    getConversionRate
  }
}
